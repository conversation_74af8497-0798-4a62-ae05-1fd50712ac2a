// pages/my/address/edit/editAddress.js
import { getPermission } from '~/utils/getPermission';
import Toast from 'tdesign-miniprogram/toast/index';
import { areaData } from '~/config/areaData';
import { userAddressAdd, userAddressUpdate, userAddressInfo } from '~/api/UserAddressApi';
import BCApi from '~/api/BCApi';
import { printNearestBuilding, calculateDistance } from '~/utils/locationUtils';

const innerPhoneReg = '^1(?:3\\d|4[4-9]|5[0-35-9]|6[67]|7[0-8]|8\\d|9\\d)\\d{8}$';// 手机号
const innerPhoneReg2 = '^(0[0-9]{2,3}\-)?([2-9][0-9]{6,7})+(\-[0-9]{1,4})?$';// 固定电话
const innerNameReg = '^[a-zA-Z\\d\\u4e00-\\u9fa5]+$';

Page({
  data: {
    areaData: areaData,
    areaPickerVisible: false,
    form: {
      id: '',
      bcId: '',
      provinceName: '',
      cityName: '',
      countyName: '',
      detailInfo: '',
      telNumber: '',
      userName: '',
      defaultFlag: 0
    },
    submitActive: false,
    selectedAreaData: [],
    bcList: [],
    currentBuildingName: '',
    currentBuildingId: '',
  },
  lastSelectedLocation: null, // 保存最后选择的位置信息
  hasSava: false,
  privateData: { verifyTips: '' },
  onLoad(options) {
    // 获取首页选择的楼宇信息
    this.initBuildingInfo();

    if (options.id) {
      this.setData({ "form.id": options.id });
      this.init(true);
    }
    // BCApi.BCList().then(res => {
    //   this.setData({ bcList: res.records });
    //   // 查找并打印离我最近的楼宇
    //   this.findAndPrintNearestBuilding(res.records);
    // });
  },
  onShow() {
    // 每次显示页面时更新楼宇信息
    this.initBuildingInfo();
  },

  // 初始化楼宇信息
  initBuildingInfo() {
    // 从本地存储获取首页选择的楼宇信息
    const selectedBuildingId = wx.getStorageSync('selectedBuildingId') || '';
    const selectedBuildingName = wx.getStorageSync('selectedBuildingName') || '';

    if (selectedBuildingName) {
      this.setData({
        currentBuildingName: selectedBuildingName,
        currentBuildingId: selectedBuildingId,
        'form.bcId': selectedBuildingId
      });

      // 验证表单合法性
      const { isLegal, tips } = this.onVerifyInputLegal();
      this.setData({ submitActive: isLegal });
      // 确保privateData存在
      if (!this.privateData) {
        this.privateData = { verifyTips: '' };
      }
      this.privateData.verifyTips = tips;
    }
  },
  // 查找并打印离我最近的楼宇
  async findAndPrintNearestBuilding(bcList) {
    try {
      const nearestBuilding = await printNearestBuilding(bcList);
      console.log('成功找到最近的楼宇:', nearestBuilding.name);
    } catch (error) {
      console.log('查找最近楼宇失败:', error.message);
    }
  },

  getWxLocation() {
    getPermission({ code: 'scope.address', name: '收货地址' }).then(() => {
      wx.chooseAddress({
        success: async (options) => {
          console.log(options)
          const { provinceName, cityName, countyName, detailInfo, userName, telNumber } = options;
          this.setData({
            "form.provinceName": provinceName,
            "form.cityName": cityName,
            "form.countyName": countyName,
            "form.detailInfo": detailInfo,
            "form.userName": userName,
            "form.telNumber": telNumber
          }, () => {
            const { isLegal, tips } = this.onVerifyInputLegal();
            this.setData({ submitActive: isLegal });
            // 确保privateData存在
            if (!this.privateData) {
              this.privateData = { verifyTips: '' };
            }
            this.privateData.verifyTips = tips;
          });
        },
        fail(err) {
          console.warn('未选择微信收货地址', err);
        },
      });
    });
  },





  // 获取楼宇经纬度
  async getBuildingCoordinates(buildingId) {
    try {
      console.log('=== 获取楼宇经纬度 ===');
      console.log('查找楼宇ID:', buildingId);

      const res = await BCApi.BCList();
      console.log('BCApi.BCList() 返回结果:', res);

      let bcList = [];
      if (res && res.records && Array.isArray(res.records)) {
        bcList = res.records;
      } else if (res && Array.isArray(res)) {
        bcList = res;
      } else if (res && res.data && Array.isArray(res.data)) {
        bcList = res.data;
      }

      console.log('解析后的楼宇列表长度:', bcList.length);
      console.log('楼宇列表:', bcList.map(b => ({ id: b.id, name: b.name, remark: b.remark })));

      const currentBuilding = bcList.find(building => building.id === buildingId);
      console.log('找到的楼宇:', currentBuilding);

      if (!currentBuilding) {
        console.warn('未找到指定ID的楼宇:', buildingId);
        return null;
      }

      if (!currentBuilding.remark) {
        console.warn('楼宇缺少经纬度信息 (remark为空):', currentBuilding.name);
        return null;
      }

      console.log('楼宇remark原始数据:', currentBuilding.remark, typeof currentBuilding.remark);

      // 解析楼宇经纬度
      let coordinates;
      if (typeof currentBuilding.remark === 'string') {
        coordinates = JSON.parse(currentBuilding.remark);
      } else if (Array.isArray(currentBuilding.remark)) {
        coordinates = currentBuilding.remark;
      } else {
        console.warn('楼宇经纬度格式不支持:', typeof currentBuilding.remark);
        return null;
      }

      console.log('解析后的坐标:', coordinates);

      if (!Array.isArray(coordinates) || coordinates.length < 2) {
        console.warn('坐标数组格式错误:', coordinates);
        return null;
      }

      const lng = parseFloat(coordinates[0]);
      const lat = parseFloat(coordinates[1]);

      console.log('解析的经纬度:', { lng, lat });

      if (isNaN(lng) || isNaN(lat)) {
        console.warn('经纬度解析失败:', { lng, lat });
        return null;
      }

      const result = { latitude: lat, longitude: lng };
      console.log('最终返回的楼宇坐标:', result);
      return result;
    } catch (error) {
      console.error('获取楼宇经纬度失败:', error);
      return null;
    }
  },



  // 显示距离警告
  showDistanceWarning(distance) {
    const distanceText = distance < 1000 ? `${Math.round(distance)}米` : `${(distance / 1000).toFixed(1)}公里`;

    wx.showModal({
      title: '地址距离提醒',
      content: `您选择的地址距离所属楼宇约${distanceText}，超出了1公里范围。请确认地址是否正确，或选择更近的楼宇。`,
      showCancel: true,
      cancelText: '重新选择楼宇',
      confirmText: '确认使用',
      success: (res) => {
        if (res.cancel) {
          // 用户选择重新选择楼宇，跳转到楼宇选择页面
          this.navigateToBuildingList();
        } else {
          // 用户确认使用该地址
          console.log('用户确认使用超距离地址');
        }
      }
    });
  },

  // 更新表单验证状态
  updateFormValidation() {
    const { isLegal, tips } = this.onVerifyInputLegal();
    this.setData({ submitActive: isLegal });
    // 确保privateData存在
    if (!this.privateData) {
      this.privateData = { verifyTips: '' };
    }
    this.privateData.verifyTips = tips;
  },

  // 跳转到楼宇选择页面
  navigateToBuildingList() {
    wx.navigateTo({
      url: '/pages/building/list/buildingList',
      events: {
        // 监听楼宇选择事件
        buildingSelected: (building) => {
          // 更新当前楼宇信息
          this.setData({
            currentBuildingName: building.name,
            currentBuildingId: building.id,
            'form.bcId': building.id
          });

          // 验证表单合法性
          const { isLegal, tips } = this.onVerifyInputLegal();
          this.setData({ submitActive: isLegal });
          // 确保privateData存在
          if (!this.privateData) {
            this.privateData = { verifyTips: '' };
          }
          this.privateData.verifyTips = tips;

          // 如果有详细地址，重新进行距离验证
          if (this.data.form.detailInfo && this.lastSelectedLocation) {
            console.log('楼宇已更换，重新进行距离验证...');
            this.validateLocationDistance(
              this.lastSelectedLocation.latitude,
              this.lastSelectedLocation.longitude
            );
          }
        }
      }
    });
  },

  // 验证选择位置与楼宇的距离
  async validateLocationDistance(latitude, longitude) {
    try {
      console.log('=== 开始距离验证 ===');
      console.log('用户选择的位置:', { latitude, longitude });

      // 获取当前选择的楼宇信息
      const currentBuildingId = this.data.currentBuildingId;
      const currentBuildingName = this.data.currentBuildingName;
      const formBcId = this.data.form.bcId;

      console.log('当前楼宇ID (currentBuildingId):', currentBuildingId);
      console.log('当前楼宇名称 (currentBuildingName):', currentBuildingName);
      console.log('表单楼宇ID (form.bcId):', formBcId);

      // 优先使用form.bcId，如果没有则使用currentBuildingId
      const buildingId = formBcId || currentBuildingId;

      if (!buildingId) {
        console.warn('未选择楼宇，跳过距离验证');
        return;
      }

      console.log('使用的楼宇ID:', buildingId);

      // 获取楼宇的经纬度信息
      const buildingCoordinates = await this.getBuildingCoordinates(buildingId);
      console.log('楼宇经纬度信息:', buildingCoordinates);

      if (!buildingCoordinates) {
        console.warn('无法获取楼宇经纬度信息，跳过距离验证');
        return;
      }

      // 计算距离
      const distance = calculateDistance(
        latitude,
        longitude,
        buildingCoordinates.latitude,
        buildingCoordinates.longitude
      );

      console.log(`地址距离楼宇: ${Math.round(distance)}米`);
      console.log('距离是否超出1公里:', distance > 1000);

      // 检查是否超出1公里
      if (distance > 1000) {
        console.log('距离超出1公里，显示警告弹窗');
        this.showDistanceWarning(distance);
      } else {
        console.log('距离验证通过');
        Toast({
          context: this,
          selector: '#t-toast',
          message: '地址距离验证通过',
          icon: 'check-circle',
          duration: 1500
        });
      }

    } catch (error) {
      console.error('验证地址距离失败:', error);
    }
  },

	  fillDetailFromLocation() {
	    // 通过定位获取附近地址并填充到“详细地址”，支持手动修改
	    const self = this; // 保存this上下文
	    wx.chooseLocation({
	      success(res) {
	        console.log('chooseLocation result:', res);

	        // 保存选择的位置信息，用于重新选择楼宇后的距离验证
	        self.lastSelectedLocation = {
	          latitude: res.latitude,
	          longitude: res.longitude
	        };

	        // 解析地址信息
	        const addressInfo = self.parseAddressFromLocation(res);

	        // 更新表单数据
	        self.setData({
	          'form.provinceName': addressInfo.province,
	          'form.cityName': addressInfo.city,
	          'form.countyName': addressInfo.district,
	          'form.detailInfo': addressInfo.detail
	        }, () => {
	          const { isLegal, tips } = self.onVerifyInputLegal();
	          self.setData({ submitActive: isLegal });
	          // 确保privateData存在
	          if (!self.privateData) {
	            self.privateData = { verifyTips: '' };
	          }
	          self.privateData.verifyTips = tips;
	        });

	        // 进行距离验证
	        console.log('开始进行距离验证...');
	        self.validateLocationDistance(res.latitude, res.longitude);
	      },
	      fail(err) {
	        console.warn('wx.chooseLocation fail:', err);
	        if (err.errMsg !== 'chooseLocation:fail cancel') {
	          Toast({ context: self, selector: '#t-toast', message: '地点选择失败，请重试', icon: '', duration: 1000 });
	        }
	      }
	    });
	  },
	  // 解析地址信息，从chooseLocation返回的地址中提取省市区
	  parseAddressFromLocation(locationResult) {
	    const { address = '', name = '' } = locationResult;

	    // 组合详细地址
	    const detail = (name && address) ? `${address} ${name}` : (address || name || '');

	    // 尝试从address中解析省市区
	    let province = '', city = '', district = '';

	    if (address) {
	      console.log('原始地址:', address);

	      // 按顺序逐级解析，避免重复匹配
	      let remainingAddress = address;

	      // 1. 提取省份（包含省、自治区、特别行政区、直辖市）
	      const provinceRegex = /^(.*?(?:省|自治区|特别行政区|市))/;
	      const provinceMatch = remainingAddress.match(provinceRegex);
	      if (provinceMatch) {
	        province = provinceMatch[1];
	        remainingAddress = remainingAddress.substring(province.length);
	        console.log('解析省份:', province, '剩余:', remainingAddress);
	      }

	      // 2. 提取市（从剩余地址中提取）
	      if (remainingAddress) {
	        const cityRegex = /^(.*?(?:市|州|盟|地区))/;
	        const cityMatch = remainingAddress.match(cityRegex);
	        if (cityMatch) {
	          city = cityMatch[1];
	          remainingAddress = remainingAddress.substring(city.length);
	          console.log('解析城市:', city, '剩余:', remainingAddress);
	        }
	      }

	      // 3. 提取区县（从剩余地址中提取）
	      if (remainingAddress) {
	        const districtRegex = /^(.*?(?:区|县|市|旗|镇))/;
	        const districtMatch = remainingAddress.match(districtRegex);
	        if (districtMatch) {
	          district = districtMatch[1];
	          console.log('解析区县:', district);
	        }
	      }

	      console.log('最终解析结果:', { province, city, district });
	    }

	    return {
	      province: province || '',
	      city: city || '',
	      district: district || '',
	      detail: detail
	    };
	  },

  async init() {
    try {
      const result = await userAddressInfo(this.data.form.id);
      this.setData({ form: result, submitActive: true });
    } catch (error) {
      console.error('err:', error);
    }
  },
  formSubmit() {
    const { submitActive } = this.data;
    if (!submitActive) {
      // 确保privateData存在
      if (!this.privateData) {
        this.privateData = { verifyTips: '' };
      }
      const message = this.privateData.verifyTips || '请完善表单信息';
      Toast({ context: this, selector: '#t-toast', message: message, icon: '', duration: 1000 });
      return;
    }
    const { form } = this.data;
    this.hasSava = true;
    wx.showLoading({ title: '正在保存...', mask: true });
    if (this.data.form.id) {
      userAddressUpdate(form).then(res => {
        this.hasSava = false;
        console.log(res);
        wx.hideLoading();
        wx.navigateBack();
      }).catch(error => {
        this.hasSava = false;
        console.log(error);
        wx.hideLoading();
      });
    } else {
      userAddressAdd(form).then(res => {
        this.hasSava = false;
        console.log(res);
        wx.hideLoading();
        wx.navigateBack();
      }).catch(error => {
        this.hasSava = false;
        console.log(error);
        wx.hideLoading();
      });
    }
  },
  onInputValue(e) {
    const { item } = e.currentTarget.dataset;
    // 移除了地区选择器相关逻辑，现在只处理其他输入字段
    const { value = '' } = e.detail;
    this.setData({ [`form.${item}`]: value },
      () => {
        const { isLegal, tips } = this.onVerifyInputLegal();
        this.setData({ submitActive: isLegal });
        // 确保privateData存在
        if (!this.privateData) {
          this.privateData = { verifyTips: '' };
        }
        this.privateData.verifyTips = tips;
      },
    );
  },
  onCheckDefaultAddress({ detail }) {
    const { value } = detail;
    this.setData({ 'form.defaultFlag': value });
  },
  // onPickArea() {
  //   this.setData({ areaPickerVisible: true });
  // },
  onVerifyInputLegal() {
    const { bcId, userName, telNumber, detailInfo } = this.data.form;
    const nameRegExp = new RegExp(String(innerNameReg));
    const phoneRegExp = new RegExp(String(innerPhoneReg));
    const phoneRegExp2 = new RegExp(String(innerPhoneReg2));

    if (!userName || !userName.trim()) { return { isLegal: false, tips: '请填写收货人' }; }
    if (!nameRegExp.test(userName)) { return { isLegal: false, tips: '收货人仅支持输入中文、英文（区分大小写）、数字' }; }
    if (!telNumber || !telNumber.trim()) { return { isLegal: false, tips: '请填写手机号' }; }
    if (!phoneRegExp.test(telNumber) && !phoneRegExp2.test(telNumber)) { return { isLegal: false, tips: '请填写正确的手机号' }; }
    // 省市区验证改为可选，因为用户界面已隐藏地区选择，主要通过定位获取
    // if (!provinceName || !provinceName.trim() || !cityName || !cityName.trim() || !countyName || !countyName.trim()) {
    //   return { isLegal: false, tips: '请选择省市区信息' };
    // }
    if (!bcId || !bcId.trim()) { return { isLegal: false, tips: '请选择所属楼宇' }; }
    if (!detailInfo || !detailInfo.trim()) { return { isLegal: false, tips: '请完善详细地址' }; }
    if (detailInfo && detailInfo.trim().length > 50) { return { isLegal: false, tips: '详细地址不能超过50个字符' }; }

    return { isLegal: true, tips: '添加成功' };
  },
  onSearchAddress() {
    getPermission({ code: 'scope.userLocation', name: '地址位置' }).then(() => {
      wx.chooseLocation({
        success: (res) => {
          if (res.name) {
            this.triggerEvent('addressParse', {
              address: res.address,
              name: res.name,
              latitude: res.latitude,
              longitude: res.longitude,
            });
          } else {
            // 确保privateData存在
            if (!this.privateData) {
              this.privateData = { verifyTips: '' };
            }
            const message = this.privateData.verifyTips || '请完善表单信息';
            Toast({ context: this, selector: '#t-toast', message: message, icon: '', duration: 1000 });
          }
        },
        fail: (res) => {
          console.warn(`wx.chooseLocation fail: ${JSON.stringify(res.errMsg)}`);
          if (res.errMsg !== 'chooseLocation:fail cancel') {
            Toast({ context: this, selector: '#t-toast', message: '地点错误，请重新选择', icon: '', duration: 1000 });
          }
        },
      });
    });
  }
})