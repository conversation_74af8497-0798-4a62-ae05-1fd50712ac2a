# 详细地址显示测试说明

## 测试目标
验证详细地址在不同状态下的显示效果和高度变化。

## 测试场景

### 1. 初始状态（无数据）
**期望效果：**
- 显示占位符文字："点击选择您的详细地址"
- 占位符文字为灰色（#999）
- 高度与其他输入框保持一致（48rpx）
- 文字垂直居中对齐
- 不换行显示

**测试步骤：**
1. 打开收货地址编辑页面
2. 观察详细地址区域的初始状态
3. 对比联系人、手机号输入框的高度

### 2. 有数据状态（短地址）
**期望效果：**
- 显示实际地址内容
- 文字颜色为正常色（#333）
- 如果地址较短，高度仍保持与输入框一致
- 文字顶部对齐（有12rpx的padding-top调整）

**测试步骤：**
1. 点击详细地址区域
2. 选择一个地址名称较短的位置
3. 确认后观察显示效果

### 3. 有数据状态（长地址）
**期望效果：**
- 显示完整地址内容
- 自动换行，高度自适应
- 文字顶部对齐
- 支持多行显示

**测试步骤：**
1. 点击详细地址区域
2. 选择一个地址名称较长的位置（如包含详细门牌号的地址）
3. 确认后观察是否正确换行和展开

### 4. 交互测试
**期望效果：**
- 点击整个地址区域都能触发选择
- 点击右侧位置图标也能触发选择
- 选择后自动填充并进行距离验证

**测试步骤：**
1. 分别点击地址文字区域和位置图标
2. 验证都能正常触发地址选择
3. 确认选择后的自动验证功能

## 关键CSS类说明

### 状态类
- `.placeholder-state`: 无数据时的包装器状态
- `.placeholder`: 占位符文字状态

### 样式要点
- 占位符状态：`align-items: center` + `line-height: 48rpx`
- 有内容状态：`align-items: flex-start` + `padding-top: 12rpx`
- 自动换行：`word-wrap: break-word` + `word-break: break-all`

## 预期问题和解决方案

### 问题1：高度不一致
**现象：** 详细地址区域与其他输入框高度不匹配
**解决：** 检查`min-height: 48rpx`和`line-height`设置

### 问题2：文字对齐问题
**现象：** 有内容时文字位置不正确
**解决：** 调整`padding-top: 12rpx`数值

### 问题3：长地址显示问题
**现象：** 长地址不能正确换行或被截断
**解决：** 确认`word-wrap`和`word-break`样式生效

## 验收标准

✅ 无数据时高度与其他输入框一致
✅ 占位符文字垂直居中显示
✅ 有数据时能正确显示内容
✅ 长地址能自动换行展开
✅ 点击任意区域都能触发选择
✅ 样式与整体设计保持一致
