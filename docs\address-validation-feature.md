# 收货地址距离验证功能

## 功能概述

在收货地址编辑页面，当用户点击详细地址右侧的位置按钮选择地址后，系统会自动验证该地址与所属楼宇的距离，如果超出1公里范围，会提示用户确认。

## 实现流程

### 1. 用户选择地址位置
- 用户可以手动输入详细地址，或点击右侧的位置图标按钮
- 点击位置按钮触发 `fillDetailFromLocation()` 方法
- 调用 `wx.chooseLocation` 接口让用户选择地址位置

### 2. 地址信息处理
- 解析选择的地址信息，自动填充省市区和详细地址
- 获取用户选择位置的经纬度坐标
- 更新表单验证状态

### 3. 自动距离验证
- 获取当前选择楼宇的经纬度信息（从楼宇数据的 `remark` 字段解析）
- 使用 Haversine 公式计算用户选择位置与楼宇的距离
- 自动进行距离判断

### 4. 距离判断结果
- 如果距离 ≤ 1公里：显示"地址距离验证通过"Toast提示
- 如果距离 > 1公里：显示距离警告对话框

### 5. 距离警告处理
- 显示具体距离信息（如"1.2公里"）
- 用户可选择：
  - "重新选择"：清空详细地址，重新选择位置
  - "确认使用"：继续使用该地址

## 关键代码文件

### 主要修改文件
- `pages/home/<USER>/address/edit/editAddress.js`

### 新增方法
1. `validateLocationDistance(latitude, longitude)` - 验证选择位置与楼宇的距离
2. `getBuildingCoordinates(buildingId)` - 获取楼宇经纬度信息
3. `showDistanceWarning(distance)` - 显示距离警告对话框
4. `updateFormValidation()` - 更新表单验证状态

### 修改方法
1. `fillDetailFromLocation()` - 在成功选择位置后自动调用距离验证

### 依赖工具函数
- `calculateDistance()` - 计算两点间距离（来自 `utils/locationUtils.js`）

## 数据结构

### 楼宇数据格式
```javascript
{
  id: "楼宇ID",
  name: "楼宇名称", 
  remark: ["经度", "纬度"] // 或 JSON 字符串格式
}
```

### 位置数据格式
```javascript
{
  latitude: 纬度,
  longitude: 经度
}
```

## 📱 用户操作流程

1. 手动输入详细地址，或点击右侧的📍位置按钮
2. 如果点击位置按钮：在地图上选择具体位置
3. 点击"确定"
4. 系统自动：
   - 填充地址信息
   - 计算与楼宇的距离
   - 显示验证结果

## 🎨 界面特点

- **详细地址输入框**：支持手动输入和位置选择两种方式
- **位置按钮**：点击右侧位置图标可快速选择地址
- **自动验证**：通过位置按钮选择地址后自动进行距离验证

## 用户体验优化

1. **非阻塞式验证**：如果获取楼宇经纬度失败或用户取消位置选择，不会阻止用户继续操作
2. **清晰的提示信息**：距离警告会显示具体的距离数值
3. **灵活的选择**：用户可以选择跳过验证或确认使用超距离地址
4. **友好的交互**：通过对话框引导用户完成验证流程
5. **简化操作**：详细地址不可手动编辑，只能通过地图选择，避免输入错误

## 注意事项

1. 需要用户授权位置权限才能进行距离验证
2. 楼宇数据中的 `remark` 字段必须包含有效的经纬度信息
3. 距离计算使用 Haversine 公式，适用于地球表面的球面距离计算
4. 1公里的距离阈值可以根据业务需求调整

## 测试建议

1. 测试正常流程：选择距离楼宇较近的地址
2. 测试异常情况：选择距离楼宇超过1公里的地址
3. 测试边界情况：用户取消位置选择、楼宇无经纬度信息等
4. 测试权限情况：用户拒绝位置权限的处理
